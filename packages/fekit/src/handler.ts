import { HttpAgent } from "@ag-ui/client";
import {
  CopilotRuntime,
  copilotRuntimeNextJSAppRouterEndpoint,
  CopilotServiceAdapter,
} from "@copilotkit/runtime";
import { NextRequest } from "next/server";
import type { Logger } from "pino";

export interface CreateYaiNexusHandlerOptions {
  backendUrl: string;
  logger: Logger; // 必填：应用提供统一的 logger
  tracing?: {
    enabled?: boolean;
    generateTraceId?: () => string;
  };
}

/**
 * Lightweight adapter that proxies requests to AG-UI HttpAgent
 * Uses dependency injection for logger to integrate with application's unified logging system
 */
class YaiNexusServiceAdapter implements CopilotServiceAdapter {
  private httpAgent: HttpAgent;
  private options: CreateYaiNexusHandlerOptions;
  public baseLogger: Logger;

  constructor(backendUrl: string, options: CreateYaiNexusHandlerOptions) {
    // 使用 /agui 端点，这个端点返回 AG-UI 对象而不是 SSE 流
    const aguiUrl = backendUrl.endsWith("/")
      ? `${backendUrl}agui`
      : `${backendUrl}/agui`;

    this.httpAgent = new HttpAgent({
      url: aguiUrl,
      description: "YAI Nexus Agent for AG-UI protocol",
    });
    this.options = options;

    // 使用注入的 logger，添加 fekit 上下文
    this.baseLogger = options.logger.child({ component: "yai-nexus-fekit" });

    // 记录 HttpAgent 初始化信息
    this.baseLogger.info("HttpAgent initialized", {
      backendUrl,
      aguiUrl,
      httpAgentUrl: this.httpAgent.url,
    });
  }

  /**
   * 创建带有请求上下文的 logger
   */
  private createRequestLogger(context: {
    traceId?: string;
    runId?: string;
    threadId?: string;
  }): Logger {
    return this.baseLogger.child(context);
  }

  private generateTraceId(): string {
    if (this.options.tracing?.generateTraceId) {
      return this.options.tracing.generateTraceId();
    }
    // 默认生成策略：trace_ + 时间戳 + 随机数
    return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  async process(request: any): Promise<any> {
    // 生成或使用现有的追踪 ID
    const traceId = this.options.tracing?.enabled
      ? this.generateTraceId()
      : undefined;
    const threadId = request.threadId || traceId || "default";
    const runId = request.runId || `run_${Date.now()}`;

    // 格式化消息，确保每个消息都有 id 字段
    const formattedMessages = (request.messages || []).map(
      (msg: any, index: number) => ({
        id: msg.id || `msg_${Date.now()}_${index}`,
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name }),
        ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
        ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
      })
    );

    // Since HttpAgent expects RunAgentInput format, we need minimal conversion
    const agentInput = {
      threadId,
      runId,
      messages: formattedMessages,
      tools: request.tools || [],
      context: request.context || [],
      state: request.state || null,
      forwardedProps: request.forwardedProps || {},
    };

    // 创建请求级别的 logger
    const requestLogger = this.createRequestLogger({
      traceId,
      runId,
      threadId,
    });

    // 记录请求开始
    if (traceId) {
      requestLogger.info("Processing non-streaming request", {
        operation: "process",
        messageCount: request.messages?.length || 0,
      });
    }

    // 先测试 HttpAgent 的基本功能
    requestLogger.info("Testing HttpAgent basic properties in process method", {
      httpAgentUrl: this.httpAgent.url,
      httpAgentDescription: this.httpAgent.description,
      agentInputKeys: Object.keys(agentInput),
      agentInputMessages: agentInput.messages?.length || 0,
    });

    try {
      // Use HttpAgent's runAgent method for non-streaming
      requestLogger.info("Calling HttpAgent.runAgent", {
        method: "runAgent",
        agentInput,
      });

      await this.httpAgent.runAgent(agentInput);

      requestLogger.info("HttpAgent.runAgent completed successfully");

      // For now, return a simple response
      // The HttpAgent handles the AG-UI protocol internally
      return {
        id: `response_${Date.now()}`,
        content: "Response from YAI Nexus backend",
        role: "assistant",
      };
    } catch (error) {
      requestLogger.error("Error in HttpAgent.runAgent", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      throw error;
    }
  }

  async *stream(request: any): AsyncIterable<any> {
    // 生成或使用现有的追踪 ID
    const traceId = this.options.tracing?.enabled
      ? this.generateTraceId()
      : undefined;
    const threadId = request.threadId || traceId || "default";
    const runId = request.runId || `run_${Date.now()}`;

    // 格式化消息，确保每个消息都有 id 字段
    const formattedMessages = (request.messages || []).map(
      (msg: any, index: number) => ({
        id: msg.id || `msg_${Date.now()}_${index}`,
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name }),
        ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
        ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
      })
    );

    // Since HttpAgent expects RunAgentInput format, we need minimal conversion
    const agentInput = {
      threadId,
      runId,
      messages: formattedMessages,
      tools: request.tools || [],
      context: request.context || [],
      state: request.state || null,
      forwardedProps: request.forwardedProps || {},
    };

    // 创建请求级别的 logger
    const requestLogger = this.createRequestLogger({
      traceId,
      runId,
      threadId,
    });

    // 记录流式请求开始
    if (traceId) {
      requestLogger.info("Processing streaming request", {
        operation: "stream",
        messageCount: request.messages?.length || 0,
      });
    }

    // 记录 HttpAgent 调用详情
    requestLogger.info("Calling HttpAgent.run", {
      agentInput,
      httpAgentUrl: this.httpAgent.url,
      backendUrl: this.options.backendUrl,
    });

    try {
      // 先测试 HttpAgent 的基本功能
      requestLogger.info("Testing HttpAgent basic properties", {
        httpAgentUrl: this.httpAgent.url,
        httpAgentDescription: this.httpAgent.description,
        agentInputKeys: Object.keys(agentInput),
        agentInputMessages: agentInput.messages?.length || 0,
      });

      // Use HttpAgent's run method for streaming
      const events$ = this.httpAgent.run(agentInput);

      requestLogger.info("HttpAgent.run returned observable", {
        observableType: typeof events$,
        hasSubscribe: typeof events$?.subscribe === "function",
        observableConstructor: events$?.constructor?.name,
      });

      // 测试 Observable 是否立即完成
      let eventCount = 0;
      let hasCompleted = false;
      let hasErrored = false;

      const testSubscription = events$.subscribe({
        next: (event: any) => {
          eventCount++;
          requestLogger.info("HttpAgent Observable emitted event", {
            eventCount,
            eventType: event?.type,
            event,
          });
        },
        error: (error: any) => {
          hasErrored = true;
          requestLogger.error("HttpAgent Observable error", {
            error: error.message,
            stack: error.stack,
          });
        },
        complete: () => {
          hasCompleted = true;
          requestLogger.info("HttpAgent Observable completed", {
            eventCount,
            hasCompleted,
            hasErrored,
          });
        },
      });

      // 等待一小段时间看看是否有事件
      await new Promise((resolve) => setTimeout(resolve, 100));

      testSubscription.unsubscribe();

      requestLogger.info("HttpAgent Observable test results", {
        eventCount,
        hasCompleted,
        hasErrored,
      });

      // Convert Observable to AsyncIterable
      yield* this.observableToAsyncIterable(events$);
    } catch (error) {
      requestLogger.error("Error in HttpAgent.run", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      // 返回错误响应
      yield {
        type: "error",
        content: "处理请求时发生错误",
        data: {
          type: "RUN_ERROR",
          message: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  private async *observableToAsyncIterable(
    observable: any
  ): AsyncIterable<any> {
    const requestLogger = this.baseLogger.child({
      method: "observableToAsyncIterable",
    });

    requestLogger.info("Converting Observable to AsyncIterable");

    try {
      // 使用 RxJS 的 lastValueFrom 来处理可能为空的 Observable
      const { lastValueFrom, toArray, defaultIfEmpty } = await import("rxjs");
      const { map } = await import("rxjs/operators");

      // 将 Observable 转换为 Promise，收集所有事件，如果为空则返回空数组
      const events: any[] = await lastValueFrom(
        observable.pipe(
          toArray(), // 收集所有事件到数组
          defaultIfEmpty([]), // 如果 Observable 为空，返回空数组
          map((eventArray: any[]) => {
            requestLogger.info("Observable completed", {
              totalEvents: eventArray.length,
            });
            return eventArray;
          })
        )
      );

      // 逐个发出事件
      for (const event of events) {
        requestLogger.debug("Processing event", {
          eventType: event?.type,
        });

        // 直接发出 AG-UI 事件，CopilotKit 应该能够处理
        yield event;
      }

      // 如果没有事件，发出默认响应
      if (events.length === 0) {
        requestLogger.warn("No events received, creating default response");
        yield {
          type: "TEXT_MESSAGE_CHUNK",
          delta: "抱歉，我现在无法处理您的请求。请稍后再试。",
          message_id: null,
          role: null,
          timestamp: null,
          raw_event: null,
        };
      }

      requestLogger.info("Observable conversion completed", {
        totalEvents: events.length,
      });
    } catch (error) {
      requestLogger.error("Error in observable conversion", {
        error: error instanceof Error ? error.message : String(error),
      });

      // 发出错误事件
      yield {
        type: "RUN_ERROR",
        message: error instanceof Error ? error.message : String(error),
        timestamp: null,
        raw_event: null,
      };
    }
  }
}

/**
 * Creates a Next.js API route handler that connects CopilotKit frontend
 * with yai-nexus-agentkit Python backend using AG-UI protocol
 *
 * @param options Configuration options for the handler
 * @returns Next.js POST handler function
 *
 * @example
 * ```typescript
 * // /src/app/api/copilotkit/route.ts
 * import { createYaiNexusHandler } from "@yai-nexus/fekit";
 * import { logger } from "@/lib/logger";
 *
 * export const POST = createYaiNexusHandler({
 *   backendUrl: process.env.PYTHON_BACKEND_URL!,
 *   logger, // 注入应用的统一 logger
 * });
 * ```
 */
export function createYaiNexusHandler(options: CreateYaiNexusHandlerOptions) {
  // Create lightweight service adapter that proxies to AG-UI HttpAgent
  const serviceAdapter = new YaiNexusServiceAdapter(
    options.backendUrl,
    options
  );

  // Create CopilotRuntime
  const runtime = new CopilotRuntime({
    middleware: {
      onBeforeRequest: async ({
        threadId,
        runId,
        inputMessages,
        properties,
      }) => {
        const logger = serviceAdapter.baseLogger.child({
          threadId,
          runId,
          phase: "before",
        });
        logger.info("CopilotRuntime request started", {
          messageCount: inputMessages.length,
          properties,
        });
      },
      onAfterRequest: async ({
        threadId,
        runId,
        inputMessages,
        outputMessages,
        properties,
      }) => {
        const logger = serviceAdapter.baseLogger.child({
          threadId,
          runId,
          phase: "after",
        });
        logger.info("CopilotRuntime request completed", {
          inputCount: inputMessages.length,
          outputCount: outputMessages.length,
          properties,
        });
      },
    },
  });

  // Create and return the Next.js POST handler
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter, // Use our lightweight adapter
    endpoint: "/api/copilotkit",
  });

  return async function POST(req: NextRequest) {
    try {
      return await handleRequest(req);
    } catch (error) {
      // 使用注入的 logger 记录错误
      serviceAdapter.baseLogger.error("Handler error", {
        error:
          error instanceof Error
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
              }
            : { message: String(error) },
        url: req.url,
        method: req.method,
      });

      // Return a proper error response
      return new Response(
        JSON.stringify({
          error: "Internal server error",
          message: error instanceof Error ? error.message : "Unknown error",
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }
  };
}

/**
 * Type alias for the return type of createYaiNexusHandler
 */
export type YaiNexusHandler = ReturnType<typeof createYaiNexusHandler>;
